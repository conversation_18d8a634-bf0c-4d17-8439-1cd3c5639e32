#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# -----------------------------------------------------------------------
# We don't support docker-compose for production environments.
# If you choose to use this type of deployment make sure to
# create you own docker environment file (docker/.env) with your own
# unique random secure passwords and SECRET_KEY.
# -----------------------------------------------------------------------
x-superset-user: &superset-user root
x-superset-depends-on: &superset-depends-on
  - db
  - redis
x-superset-volumes: &superset-volumes
  # /app/pythonpath_docker will be appended to the PYTHONPATH in the final container
  - ./docker:/app/docker
  - ./superset:/app/superset
  - ./superset-frontend:/app/superset-frontend
  - superset_home:/app/superset_home
  - ./tests:/app/tests

x-common-build: &common-build
  context: .
  target: dev
  cache_from:
    - apache/superset-cache:3.10-slim-bookworm

services:
  # nginx:
  #   image: nginx:latest
  #   container_name: superset_nginx
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #   extra_hosts:
  #     - "host.docker.internal:host-gateway"
  #   volumes:
  #     - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  redis:
    image: redis:7
    container_name: superset_cache
    restart: unless-stopped
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis:/data

  db:
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    image: postgres:15
    container_name: superset_db
    restart: unless-stopped
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - db_home:/var/lib/postgresql/data
      - ./docker/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d

  superset:
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    build:
      <<: *common-build
    container_name: superset_app
    command: ["/app/docker/docker-bootstrap.sh", "app"]
    restart: unless-stopped
    cap_add:
      - SYS_PTRACE
    ports:
      - 8088:8088
      - 5678:5678
    extra_hosts:
      - "host.docker.internal:host-gateway"
    user: *superset-user
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"

  superset-websocket:
    container_name: superset_websocket
    build: ./superset-websocket
    ports:
      - 8080:8080
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - redis
    # Mount everything in superset-websocket into container and
    # then exclude node_modules and dist with bogus volume mount.
    # This is necessary because host and container need to have
    # their own, separate versions of these files. .dockerignore
    # does not seem to work when starting the service through
    # docker compose.
    #
    # For example, node_modules may contain libs with native bindings.
    # Those bindings need to be compiled for each OS and the container
    # OS is not necessarily the same as host OS.
    volumes:
      - ./superset-websocket:/home/<USER>
      - /home/<USER>/node_modules
      - /home/<USER>/dist

      # Mounting a config file that contains a dummy secret required to boot up.
      # do not use this docker-compose in production
      - ./docker/superset-websocket/config.json:/home/<USER>/config.json
    environment:
      - PORT=8080
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_SSL=false

  superset-init:
    build:
      <<: *common-build
    container_name: superset_init
    command: ["/app/docker/docker-init.sh"]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    depends_on: *superset-depends-on
    user: *superset-user
    volumes: *superset-volumes
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
    healthcheck:
      disable: true

  superset-node:
    image: node:18
    environment:
      # set this to false if you have perf issues running the npm i; npm run dev in-docker
      # if you do so, you have to run this manually on the host, which should perform better!
      SCARF_ANALYTICS: "false"
    container_name: superset_node
    command: ["/app/docker/docker-frontend.sh"]
    env_file:
      - path: docker/.env # default
        required: true
      - path: docker/.env-local # optional override
        required: false
    depends_on: *superset-depends-on
    volumes: *superset-volumes

  # superset-worker:
  #   build:
  #     <<: *common-build
  #   container_name: superset_worker
  #   command: ["/app/docker/docker-bootstrap.sh", "worker"]
  #   env_file:
  #     - path: docker/.env # default
  #       required: true
  #     - path: docker/.env-local # optional override
  #       required: false
  #   environment:
  #     CELERYD_CONCURRENCY: 2
  #   restart: unless-stopped
  #   depends_on: *superset-depends-on
  #   user: *superset-user
  #   volumes: *superset-volumes
  #   extra_hosts:
  #     - "host.docker.internal:host-gateway"
  #   healthcheck:
  #     test: ["CMD-SHELL", "celery -A superset.tasks.celery_app:app inspect ping -d celery@$$HOSTNAME"]
    # Bump memory limit if processing selenium / thumbnails on superset-worker
    # mem_limit: 2038m
    # mem_reservation: 128M

  # superset-worker-beat:
  #   build:
  #     <<: *common-build
  #   container_name: superset_worker_beat
  #   command: ["/app/docker/docker-bootstrap.sh", "beat"]
  #   env_file:
  #     - path: docker/.env # default
  #       required: true
  #     - path: docker/.env-local # optional override
  #       required: false
  #   restart: unless-stopped
  #   depends_on: *superset-depends-on
  #   user: *superset-user
  #   volumes: *superset-volumes
  #   healthcheck:
  #     disable: true

  # superset-tests-worker:
  #   build:
  #     <<: *common-build
  #   container_name: superset_tests_worker
  #   command: ["/app/docker/docker-bootstrap.sh", "worker"]
  #   env_file:
  #     - path: docker/.env # default
  #       required: true
  #     - path: docker/.env-local # optional override
  #       required: false
  #   profiles:
  #     - optional
  #   environment:
  #     DATABASE_HOST: localhost
  #     DATABASE_DB: test
  #     REDIS_CELERY_DB: 2
  #     REDIS_RESULTS_DB: 3
  #     REDIS_HOST: localhost
  #     CELERYD_CONCURRENCY: 8
  #   network_mode: host
  #   depends_on: *superset-depends-on
  #   user: *superset-user
  #   volumes: *superset-volumes
  #   healthcheck:
  #     test: ["CMD-SHELL", "celery inspect ping -A superset.tasks.celery_app:app -d celery@$$HOSTNAME"]

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false
