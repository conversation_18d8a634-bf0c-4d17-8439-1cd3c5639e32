# DODO was here
from __future__ import annotations

import datetime
import logging
import math
from typing import Any, TYPE_CHECKING

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)

if TYPE_CHECKING:
    from superset.common.query_object import QueryObject


def left_join_df(
    left_df: pd.DataFrame,
    right_df: pd.DataFrame,
    join_keys: list[str],
    lsuffix: str = "",
    rsuffix: str = "",
) -> pd.DataFrame:
    df = left_df.set_index(join_keys).join(
        right_df.set_index(join_keys), lsuffix=lsuffix, rsuffix=rsuffix
    )
    df.reset_index(inplace=True)
    return df


def full_outer_join_df(
    left_df: pd.DataFrame,
    right_df: pd.DataFrame,
    lsuffix: str = "",
    rsuffix: str = "",
) -> pd.DataFrame:
    df = left_df.join(right_df, lsuffix=lsuffix, rsuffix=rsuffix, how="outer")
    df.reset_index(inplace=True)
    return df


def df_metrics_to_num(df: pd.DataFrame, query_object: QueryObject) -> None:
    """Converting metrics to numeric when pandas.read_sql cannot"""
    for col, dtype in df.dtypes.items():
        if dtype.type == np.object_ and col in query_object.metric_names:
            # soft-convert a metric column to numeric
            # will stay as strings if conversion fails
            df[col] = df[col].infer_objects()


def is_datetime_series(series: Any) -> bool:
    if series is None or not isinstance(series, pd.Series):
        return False

    if series.isnull().all():
        return False

    return pd.api.types.is_datetime64_any_dtype(series) or (
        series.apply(lambda x: isinstance(x, datetime.date) or x is None).all()
    )


# DODO added start: CSV/XLSX export logic #52459208
# pylint: disable=logging-fstring-interpolation,too-many-branches
def convert_to_time(value: Any) -> str:
    from decimal import Decimal  # pylint: disable=import-outside-toplevel

    # Handle None/NaN cases
    if value is None:
        logger.warning("Export Debug - Value is None, returning 00:00:00")
        return "00:00:00"

    # Handle string values that might be numeric
    if isinstance(value, str):
        # If it's already formatted as time, return as-is
        if ":" in value:
            return value
        # Try to convert string to number
        try:
            value = float(value)
        except (ValueError, TypeError):
            logger.warning(
                f"Export Debug - Could not convert string to number: {value}"
            )
            return value

    # Handle Decimal values (DODO added fix for decimal.Decimal)
    if isinstance(value, Decimal):
        try:
            value = float(value)
        except (ValueError, TypeError):
            logger.warning(
                f"Export Debug - Could not convert Decimal to float: {value}"
            )
            return "00:00:00"

    # Handle numeric values (int, float, or converted Decimal)
    if isinstance(value, (int, float)):
        # Check for NaN
        if math.isnan(value):
            logger.warning("Export Debug - Value is NaN, returning 00:00:00")
            return "00:00:00"

        # DODO fixed: Always treat duration values as milliseconds for consistency with UI
        # The UI displays duration values by treating them as milliseconds
        # So export should do the same to maintain consistency
        total_seconds = int(value // 1000)

        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        result = f"{hours:02}:{minutes:02}:{seconds:02}"
        return result

    # Fallback for other types
    logger.warning(
        f"Export Debug - Unexpected value type: {type(value)}, returning 00:00:00"
    )
    return "00:00:00"


# DODO added stop: CSV/XLSX export logic #52459208


# DODO added start:CSV/XLSX export logic #52459208
def format_data_for_export(
    df: pd.DataFrame,
    form_data: dict[str, Any] | None = None,
    column_translations: dict[str, str] | None = None,  # pylint: disable=unused-argument
    sorting_column_mapping: dict[str, str] | None = None,
) -> pd.DataFrame:
    form_data = form_data or {}

    export_as_time = form_data.get(
        "export_as_time"
    )  # с фронта приходит поле "export_as_time", если тип графика big number и значение нужно экспортнуть как время
    table_order_by = form_data.get(
        "table_order_by", {}
    )  # используем для сортировки данных, приходит словарь, где ключ это колонка, по которому отсортировали, а значение это в каком порядке было отсортировано

    if export_as_time:  # экспорт в формате времени
        key_column = df.keys()[0]
        df[key_column] = df[key_column].apply(convert_to_time)

    for column in df.columns:
        if pd.api.types.is_datetime64tz_dtype(df[column]):
            # timezones are not supported
            df[column] = df[column].dt.tz_localize(None)

    # DODO added: exportAsTime logic moved to apply_export_as_time_after_translation for stability
    # The old logic here was unreliable due to column name mismatches
    # Now handled by stable query metadata mapping in apply_export_as_time_after_translation

    # Сортировка данных с поддержкой переводов
    sorting_column_mapping = sorting_column_mapping or {}

    # Always reset index first to ensure clean export (removes original row indices)
    # This is crucial when data comes pre-sorted from database queries
    if not isinstance(df.index, pd.RangeIndex):
        logger.info("Export Debug - Resetting non-RangeIndex to ensure clean export")
        df.reset_index(drop=True, inplace=True)

    # Only apply user sorting if table_order_by is not empty
    # If empty, preserve the default sorting from database query
    if table_order_by:
        logger.info("Export Debug - User sorting detected, applying custom sort order")
        for column, order in table_order_by.items():
            # First try to find the column directly
            sort_column = None
            if column in df.columns:
                sort_column = column
            # If not found, try to find the actual DataFrame column using sorting mapping
            elif column in sorting_column_mapping:
                sort_column = sorting_column_mapping[column]
                logger.info(
                    f"Export Debug - Sorting: mapped translated column '{column}' to DataFrame column '{sort_column}'"
                )

            if sort_column:
                df.sort_values(
                    by=[sort_column], ascending=(order == "asc"), inplace=True
                )
                # Reset index after sorting to ensure proper row ordering in export
                df.reset_index(drop=True, inplace=True)
                logger.info(
                    f"Export Debug - Applied user sorting: column='{sort_column}', order='{order}'"
                )
            else:
                logger.warning(
                    f"Export Debug - Could not find column for sorting: '{column}'. Available columns: {list(df.columns)}"
                )
    else:
        logger.info(
            "Export Debug - No user sorting specified, preserving default database sort order"
        )

    return df
    # DODO added stop:CSV/XLSX export logic #52459208


# DODO added start: CSV/XLSX export logic #52459208
# pylint: disable=invalid-name,too-many-locals
def apply_export_as_time_after_translation(
    df: pd.DataFrame,
    form_data: dict[str, Any] | None = None,
    column_translations: dict[str, str] | None = None,
    stable_metric_mapping: dict[str, str] | None = None,
) -> pd.DataFrame:
    """
    Apply exportAsTime formatting using stable column mapping.
    Uses the same query metadata approach as the translation logic for 100% stability.
    """
    form_data = form_data or {}
    column_translations = column_translations or {}
    column_config = form_data.get("column_config", {})

    if not column_config:
        logger.info("Export Debug - No column_config found")
        return df

    metric_to_df_column = stable_metric_mapping or {}

    # If we have translations, update the mapping to point to translated column names
    if column_translations and metric_to_df_column:
        updated_mapping = {}
        for original_metric, original_df_col in metric_to_df_column.items():
            if original_metric in column_translations:
                # This metric was translated, so the DataFrame column is now the translated name
                translated_name = column_translations[original_metric]
                updated_mapping[original_metric] = translated_name
                logger.info(
                    f"Export Debug - Updated mapping for translation: {original_metric} -> {translated_name}"
                )
            else:
                # This metric was not translated, keep original mapping
                updated_mapping[original_metric] = original_df_col
        metric_to_df_column = updated_mapping

    # Fallback: if no stable mapping available, try direct mapping
    if not metric_to_df_column:
        logger.warning(
            "Export Debug - No stable mapping available, using fallback mapping"
        )
        for df_col in df.columns:
            metric_to_df_column[df_col] = df_col

    # Apply exportAsTime conversion using 100% stable mapping
    for config_key, config_value in column_config.items():
        if not config_value.get("exportAsTime"):
            continue

        # Find the actual column name using ONLY stable mapping
        target_column = None

        # Use stable mapping from query metadata (100% reliable)
        if config_key in metric_to_df_column:
            target_column = metric_to_df_column[config_key]

        # Handle translated columns: find the original metric that was translated
        elif column_translations:
            # Look for the original metric name that maps to this config_key through translation
            for original_metric, translated_name in column_translations.items():
                if (
                    translated_name == config_key
                    and original_metric in metric_to_df_column
                ):
                    target_column = metric_to_df_column[original_metric]
                    break

        # Apply conversion if we found the target column
        if target_column and isinstance(df.get(target_column), pd.Series):
            df[target_column] = df[target_column].apply(convert_to_time)
        else:
            logger.warning(
                f"Export Debug - Could not find target column for config_key: {config_key}. Available mappings: {metric_to_df_column}"
            )

    return df


# DODO added stop: CSV/XLSX export logic #52459208
